// Jazz types adapted for ReelMind integration

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  type?: 'text' | 'image' | 'tool_call'
  tool_calls?: ToolCall[]
  images?: string[]
}

export interface AssistantMessage extends Message {
  role: 'assistant'
  tool_calls?: ToolCall[]
}

export interface ToolCall {
  id: string
  type: 'function'
  function: {
    name: ToolCallFunctionName
    arguments: string
  }
}

export type ToolCallFunctionName = 
  | 'generate_image'
  | 'prompt_user_multi_choice'
  | 'prompt_user_single_choice'
  | 'write_plan'
  | 'finish'

export interface Model {
  provider: string
  model: string
  type: 'text' | 'image'
  url: string
}

export interface LLMConfig {
  models: { [key: string]: { type: 'text' | 'image' } }
  url: string
  api_key: string
  max_tokens?: number
}

export interface Session {
  id: string
  name: string
  created_at: string
  updated_at: string
  messages: Message[]
}

export interface Canvas {
  id: string
  name: string
  data: any
  thumbnail?: string
  sessions: Session[]
  created_at: string
  updated_at: string
}

export type PendingType = 'text' | 'image' | 'tool_call'

export interface ChatConfig {
  textModel: Model
  imageModel: Model
  systemPrompt: string
}
