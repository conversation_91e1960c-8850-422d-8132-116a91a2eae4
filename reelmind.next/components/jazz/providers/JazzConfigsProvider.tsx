"use client"

import { createContext, useContext, useState, ReactNode } from 'react'

interface JazzConfigsContextType {
  initCanvas: boolean
  setInitCanvas: (init: boolean) => void
}

const JazzConfigsContext = createContext<JazzConfigsContextType | undefined>(undefined)

export const useJazzConfigs = () => {
  const context = useContext(JazzConfigsContext)
  if (!context) {
    throw new Error('useJazzConfigs must be used within a JazzConfigsProvider')
  }
  return context
}

interface JazzConfigsProviderProps {
  children: ReactNode
}

export function JazzConfigsProvider({ children }: JazzConfigsProviderProps) {
  const [initCanvas, setInitCanvas] = useState(false)

  const value = {
    initCanvas,
    setInitCanvas,
  }

  return (
    <JazzConfigsContext.Provider value={value}>
      {children}
    </JazzConfigsContext.Provider>
  )
}
