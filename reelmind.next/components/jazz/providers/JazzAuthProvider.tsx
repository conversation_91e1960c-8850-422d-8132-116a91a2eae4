"use client"

import { createContext, useContext, useState, ReactNode } from 'react'

interface JazzAuthContextType {
  isAuthenticated: boolean
  user: any | null
  login: (user: any) => void
  logout: () => void
}

const JazzAuthContext = createContext<JazzAuthContextType | undefined>(undefined)

export const useJazzAuth = () => {
  const context = useContext(JazzAuthContext)
  if (!context) {
    throw new Error('useJazzAuth must be used within a JazzAuthProvider')
  }
  return context
}

interface JazzAuthProviderProps {
  children: ReactNode
}

export function JazzAuthProvider({ children }: JazzAuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<any | null>(null)

  const login = (userData: any) => {
    setUser(userData)
    setIsAuthenticated(true)
  }

  const logout = () => {
    setUser(null)
    setIsAuthenticated(false)
  }

  const value = {
    isAuthenticated,
    user,
    login,
    logout,
  }

  return (
    <JazzAuthContext.Provider value={value}>
      {children}
    </JazzAuthContext.Provider>
  )
}
