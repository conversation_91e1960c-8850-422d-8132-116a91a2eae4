"use client"

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createContext, useContext, ReactNode } from 'react'
import { JazzThemeProvider } from './JazzThemeProvider'
import { JazzConfigsProvider } from './JazzConfigsProvider'
import { JazzAuthProvider } from './JazzAuthProvider'

// Create a Jazz-specific query client to avoid conflicts
const jazzQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
})

interface JazzContextType {
  queryClient: QueryClient
}

const JazzContext = createContext<JazzContextType | undefined>(undefined)

export const useJazzContext = () => {
  const context = useContext(JazzContext)
  if (!context) {
    throw new Error('useJazzContext must be used within a JazzProvider')
  }
  return context
}

interface JazzProviderProps {
  children: ReactNode
}

export function JazzProvider({ children }: JazzProviderProps) {
  return (
    <JazzContext.Provider value={{ queryClient: jazzQueryClient }}>
      <QueryClientProvider client={jazzQueryClient}>
        <JazzThemeProvider>
          <JazzAuthProvider>
            <JazzConfigsProvider>
              <div className="jazz-app-container">
                {children}
              </div>
            </JazzConfigsProvider>
          </JazzAuthProvider>
        </JazzThemeProvider>
      </QueryClientProvider>
    </JazzContext.Provider>
  )
}
