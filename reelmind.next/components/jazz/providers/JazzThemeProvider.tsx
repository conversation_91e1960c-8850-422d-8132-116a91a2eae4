"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

type Theme = 'dark' | 'light' | 'system'

interface JazzThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const JazzThemeContext = createContext<JazzThemeContextType | undefined>(undefined)

export const useJazzTheme = () => {
  const context = useContext(JazzThemeContext)
  if (!context) {
    throw new Error('useJazzTheme must be used within a JazzThemeProvider')
  }
  return context
}

interface JazzThemeProviderProps {
  children: ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

export function JazzThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'jazz-ui-theme',
}: JazzThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)

  useEffect(() => {
    const storedTheme = localStorage.getItem(storageKey) as Theme
    if (storedTheme) {
      setTheme(storedTheme)
    }
  }, [storageKey])

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light'
      root.classList.add(systemTheme)
    } else {
      root.classList.add(theme)
    }
  }, [theme])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      localStorage.setItem(storageKey, theme)
      setTheme(theme)
    },
  }

  return (
    <JazzThemeContext.Provider value={value}>
      {children}
    </JazzThemeContext.Provider>
  )
}
