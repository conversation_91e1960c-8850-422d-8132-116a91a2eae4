import { jazzApiClient } from './client'

export const getCanvasList = async () => {
  const response = await jazzApiClient.get('/api/canvas/list')
  return response
}

export const createCanvas = async (payload: {
  canvas_id: string
  name: string
  messages: any[]
  session_id: string
  text_model: any
  image_model: any
  system_prompt: string
}) => {
  const response = await jazzApiClient.post('/api/canvas/create', payload)
  return response
}

export const getCanvas = async (id: string) => {
  const response = await jazzApiClient.get(`/api/canvas/${id}`)
  return response
}

export const saveCanvas = async (id: string, data: any, thumbnail?: string) => {
  const response = await jazzApiClient.post(`/api/canvas/${id}/save`, {
    data,
    thumbnail,
  })
  return response
}

export const renameCanvas = async (id: string, name: string) => {
  const response = await jazzApiClient.post(`/api/canvas/${id}/rename`, {
    name,
  })
  return response
}

export const deleteCanvas = async (id: string) => {
  const response = await jazzApiClient.delete(`/api/canvas/${id}/delete`)
  return response
}
