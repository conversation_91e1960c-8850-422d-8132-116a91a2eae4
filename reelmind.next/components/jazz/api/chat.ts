import { jazzApiClient } from './client'
import { Message, Model } from '../types/types'

export const getChatSession = async (sessionId: string) => {
  const response = await jazzApiClient.get<Message[]>(`/api/chat_session/${sessionId}`)
  return response
}

export const sendMessages = async (payload: {
  sessionId: string
  canvasId: string
  newMessages: Message[]
  textModel: Model
  imageModel: Model
  systemPrompt: string | null
}) => {
  const response = await jazzApiClient.post<Message[]>(`/api/chat`, {
    messages: payload.newMessages,
    canvas_id: payload.canvasId,
    session_id: payload.sessionId,
    text_model: payload.textModel,
    image_model: payload.imageModel,
    system_prompt: payload.systemPrompt,
  })
  return response
}

export const cancelChat = async (sessionId: string) => {
  const response = await jazzApiClient.post(`/api/cancel/${sessionId}`)
  return response
}
