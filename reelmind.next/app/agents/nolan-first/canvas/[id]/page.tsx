"use client"

import { Suspense } from "react"
import JazzCanvas from "@/components/jazz/JazzCanvas"

interface CanvasPageProps {
  params: {
    id: string
  }
}

export default function CanvasPage({ params }: CanvasPageProps) {
  return (
    <div className="h-screen w-full">
      <Suspense fallback={<div className="flex items-center justify-center h-full">Loading Canvas...</div>}>
        <JazzCanvas canvasId={params.id} />
      </Suspense>
    </div>
  )
}
